import 'dart:developer';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/form_firestore_service.dart';
import 'package:pdl_superapp/utils/logger_service.dart';

class RecruitmentListController extends BaseControllers {
  // Service untuk Firestore
  final FormFirestoreService _firestoreService = FormFirestoreService();

  // List untuk menyimpan form dengan status draft
  RxList<RecruitmentFormModel> draftForms = <RecruitmentFormModel>[].obs;

  // Status loading
  RxBool isLoadingForms = false.obs;

  // Filter pencarian
  RxString searchQuery = ''.obs;

  @override
  void onInit() {
    super.onInit();
    fetchDraftForms();
  }

  // Mengambil form dengan status draft
  Future<void> fetchDraftForms() async {
    isLoadingForms.value = true;

    try {
      final forms = await _firestoreService.getRecruitmentFormsByStatus(
        'draft',
      );
      draftForms.value = forms;
    } catch (e) {
      try {
        Get.find<LoggerService>().log('Error fetching draft forms: $e');
      } catch (_) {
        log('Error fetching draft forms: $e');
      }
    } finally {
      isLoadingForms.value = false;
    }
  }

  // Filter form berdasarkan nama
  List<RecruitmentFormModel> get filteredForms {
    if (searchQuery.value.isEmpty) {
      return draftForms;
    }

    return draftForms.where((form) {
      final name = form.namaKtp?.toLowerCase() ?? '';
      return name.contains(searchQuery.value.toLowerCase());
    }).toList();
  }

  // Update filter pencarian
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  // Navigasi ke halaman form untuk melanjutkan pengisian
  void continueForm(String formId) {
    Get.toNamed(Routes.KEAGENAN_FORM, parameters: {'formId': formId});
  }
}
