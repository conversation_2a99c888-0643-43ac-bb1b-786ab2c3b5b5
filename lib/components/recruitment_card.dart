import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class RecruitmentCard extends StatelessWidget {
  final RecruitmentFormModel form;
  final Function()? onTap;

  const RecruitmentCard({super.key, required this.form, this.onTap});

  @override
  Widget build(BuildContext context) {
    // Ambil nama dari form atau tampilkan placeholder jika kosong
    final String name =
        form.namaKtp?.isNotEmpty == true ? form.namaKtp! : 'Kandidat Baru';

    // Ambil informasi recruiter
    final String recruiterInfo =
        form.recruiterName?.isNotEmpty == true
            ? 'Recruiter: ${form.recruiterName}'
            : '';

    // Ambil informasi level kandidat
    final String candidateLevel =
        form.candidateLevel?.isNotEmpty == true
            ? 'Level: ${form.candidateLevel}'
            : '';

    // Format tanggal terakhir diupdate
    final String lastUpdated =
        form.lastUpdated != null
            ? 'Terakhir diupdate: ${_formatTimestamp(form.lastUpdated!)}'
            : '';

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: Get.width,
        padding: EdgeInsets.only(
          left: paddingMedium,
          right: paddingMedium,
          top: paddingMedium,
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Avatar dengan inisial nama
                CircleAvatar(
                  backgroundColor: kColorGlobalBlue,
                  child: Text(
                    Utils.getInitials(name),
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(width: paddingSmall),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              name,
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(fontWeight: FontWeight.w700),
                            ),

                            // Tampilkan level kandidat jika ada
                            if (candidateLevel.isNotEmpty)
                              Padding(
                                padding: EdgeInsets.only(
                                  top: paddingExtraSmall,
                                ),
                                child: Text(
                                  candidateLevel,
                                  style: Theme.of(context).textTheme.bodySmall,
                                ),
                              ),

                            if (lastUpdated.isNotEmpty)
                              Padding(
                                padding: EdgeInsets.only(
                                  top: paddingExtraSmall,
                                ),
                                child: Text(
                                  lastUpdated,
                                  style: Theme.of(context).textTheme.bodySmall,
                                ),
                              ),
                            SizedBox(height: paddingMedium),
                            Row(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: kColorGlobalBgBlue,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding: EdgeInsets.all(paddingExtraSmall),
                                  child: Text(
                                    'Draft',
                                    style: Theme.of(context).textTheme.bodySmall
                                        ?.copyWith(color: kColorGlobalBlue),
                                  ),
                                ),
                                SizedBox(width: paddingSmall),
                                // Tampilkan Form ID
                                Text(
                                  'Form ID: ${form.id?.substring(0, 8) ?? ""}...',
                                  style: Theme.of(
                                    context,
                                  ).textTheme.bodySmall?.copyWith(
                                    color:
                                        Get.isDarkMode
                                            ? kColorTextTersier
                                            : kColorTextTersierLight,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      CircleAvatar(
                        backgroundColor: kColorGlobalBgBlue,
                        child: Icon(
                          Icons.chevron_right_outlined,
                          color: kColorGlobalBlue,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: paddingSmall),
            Divider(),
          ],
        ),
      ),
    );
  }

  // Format timestamp menjadi string tanggal dan waktu yang mudah dibaca
  String _formatTimestamp(int timestamp) {
    final DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} hari yang lalu';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} jam yang lalu';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} menit yang lalu';
    } else {
      return 'Baru saja';
    }
  }
}
